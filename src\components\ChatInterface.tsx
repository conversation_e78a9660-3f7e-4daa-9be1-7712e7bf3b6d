
import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Send, Paperclip, GripHorizontal, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import MessageDisplay from "@/components/MessageDisplay";
import { useToast } from "@/hooks/use-toast";
import ImageUploadButton from "@/components/ImageUploadButton";
import WelcomeCard from "@/components/WelcomeCard";
import QuotedTextIndicator from "@/components/QuotedTextIndicator";

interface Message {
  role: "user" | "assistant";
  content: string;
  image?: string; // Base64 encoded image
}

const ChatInterface = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [selectedText, setSelectedText] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement | null>(null);
  const { toast } = useToast();
  const [textareaHeight, setTextareaHeight] = useState<number>(44); // Default height
  const textareaRef = useRef<HTMLTextAreaElement | null>(null);
  const resizeHandleRef = useRef<HTMLDivElement | null>(null);
  const isResizing = useRef<boolean>(false);
  const startY = useRef<number>(0);
  const startHeight = useRef<number>(0);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Effect to handle textarea auto-resize when component mounts or input changes
  useEffect(() => {
    if (textareaRef.current && input) {
      // Reset height to get the correct scrollHeight
      textareaRef.current.style.height = 'auto';
      // Set new height based on scrollHeight, with min and max constraints
      const newHeight = Math.min(
        Math.max(44, textareaRef.current.scrollHeight),
        200
      );
      setTextareaHeight(newHeight);
      textareaRef.current.style.height = `${newHeight}px`;
    }
  }, [input]);

  // Remove the useEffect that loads API key from localStorage

  useEffect(() => {
    if (selectedImage) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result as string);
      };
      reader.readAsDataURL(selectedImage);
    } else {
      setPreviewImage(null);
    }
  }, [selectedImage]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleImageSelect = (file: File) => {
    // Check file size (OpenAI has a 20MB limit, but we'll use 5MB for better performance)
    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast({
        title: "File too large",
        description: "Please select an image smaller than 5MB",
        variant: "destructive",
      });
      return;
    }

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please select a valid image file",
        variant: "destructive",
      });
      return;
    }

    setSelectedImage(file);
  };

  const clearSelectedImage = () => {
    setSelectedImage(null);
    setPreviewImage(null);
  };

  // Handle text selection from messages
  const handleTextSelect = (text: string) => {
    // Just store the selected text in state, don't add it to the input
    setSelectedText(text);

    // Focus the input field
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 100);
  };

  // Clear the selected text
  const clearSelectedText = () => {
    setSelectedText(null);
    // Remove the quote from the input if it starts with it
    if (input.startsWith('> ')) {
      const lines = input.split('\n\n');
      if (lines.length > 1) {
        setInput(lines.slice(1).join('\n\n'));
      } else {
        setInput('');
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() && !selectedImage) return;

    // Prepare the message content, adding the quoted text if present
    let messageContent = input.trim();

    // If there's selected text, add it as a quote at the beginning of the message
    if (selectedText) {
      messageContent = `> ${selectedText}\n\n${messageContent}`;
    }

    let userMessage: Message = {
      role: "user",
      content: messageContent || "" // Ensure empty string if only whitespace
    };

    // If image is selected, add it to the message
    if (selectedImage && previewImage) {
      userMessage.image = previewImage;
    }

    // Add user message
    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsProcessing(true);
    clearSelectedImage();
    setSelectedText(null);

    try {
      // Prepare messages for the API - include image data for OpenAI Vision
      // We only need to send user and assistant messages - system message is added server-side
      const apiMessages = messages
        .concat(userMessage)
        .filter(msg => msg.role === 'user' || msg.role === 'assistant')
        .map(({ role, content, image }) => {
          // For messages with images, format according to OpenAI Vision API
          if (image && role === 'user') {
            return {
              role,
              content: [
                {
                  type: "text",
                  text: content || "Please analyze this image and help me with my academic question."
                },
                {
                  type: "image_url",
                  image_url: {
                    url: image
                  }
                }
              ]
            };
          }
          // For text-only messages, use simple format
          return {
            role,
            content
          };
        });

      // Log the messages being sent to the server
      console.log("Client sending messages to server:",
        JSON.stringify(apiMessages.map(m => ({
          role: m.role,
          content: typeof m.content === 'string' ? m.content.substring(0, 20) + '...' : '[Vision Message]'
        })))
      );

      // Use our server API endpoint instead of calling OpenAI directly
      console.log("Sending request to server with messages:", apiMessages.length);

      let response: Response;
      try {
        response = await fetch("/api/chat", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            messages: apiMessages,
          }),
        });
      } catch (fetchError) {
        console.error("Network error:", fetchError);
        throw new Error("Failed to connect to the server. Please check if the server is running.");
      }

      if (!response.ok) {
        let errorMessage = "Failed to get response";
        try {
          const errorData = await response.json();
          // Handle rate limiting errors
          if (response.status === 429) {
            errorMessage = errorData.message || "Rate limit exceeded. Please try again later.";
          } else {
            errorMessage = errorData.error?.message || errorData.message || "Failed to get response";
          }
        } catch (parseError) {
          console.error("Error parsing error response:", parseError);
          // If we can't parse the error response, use a generic message
          errorMessage = response.status === 429
            ? "Rate limit exceeded. Please try again later."
            : `Server error (${response.status})`;
        }
        throw new Error(errorMessage);
      }

      // Get the response text first to avoid JSON parsing errors
      const responseText = await response.text();
      let data: any;
      try {
        data = JSON.parse(responseText);
        const assistantMessage: Message = {
          role: "assistant",
          content: data.choices[0].message.content,
        };

        setMessages((prev) => [...prev, assistantMessage]);
      } catch (parseError) {
        console.error("Error parsing success response:", parseError);
        throw new Error("Failed to parse the response from the server");
      }
    } catch (error) {
      console.error("Error fetching response:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to communicate with the server",
        variant: "destructive",
      });

      // Add error message
      setMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content: "I'm sorry, I encountered an error processing your request. Please try again later.",
        },
      ]);
    } finally {
      setIsProcessing(false);
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Submit on Enter unless Shift is pressed
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    } else if (e.key === 'Enter' && e.shiftKey) {
      // For Shift+Enter, we let the default behavior happen (new line)
      // But we also want to make sure the textarea resizes properly
      if (textareaRef.current) {
        // Allow a small delay for the new line to be added to the textarea
        setTimeout(() => {
          // Adjust height based on content
          const newHeight = Math.min(
            Math.max(44, textareaRef.current?.scrollHeight || 44),
            200
          );
          setTextareaHeight(newHeight);
        }, 0);
      }
    }
  };

  // New function to clear all messages
  const clearAllMessages = () => {
    setMessages([]);
    toast({
      title: "Chat cleared",
      description: "Started a new conversation",
    });
  };

  useEffect(() => {
    const handleMouseDown = (e: MouseEvent) => {
      if (resizeHandleRef.current && resizeHandleRef.current.contains(e.target as Node)) {
        isResizing.current = true;
        startY.current = e.clientY;
        startHeight.current = textareaHeight;
        document.body.style.cursor = "ns-resize";
      }
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing.current) return;

      const deltaY = startY.current - e.clientY;
      const newHeight = Math.min(Math.max(44, startHeight.current + deltaY), 200);
      setTextareaHeight(newHeight);

      if (textareaRef.current) {
        textareaRef.current.style.height = `${newHeight}px`;
      }
    };

    const handleMouseUp = () => {
      isResizing.current = false;
      document.body.style.cursor = "";
    };

    document.addEventListener("mousedown", handleMouseDown);
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);

    return () => {
      document.removeEventListener("mousedown", handleMouseDown);
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [textareaHeight]);

  return (
    <div className="flex flex-col min-h-screen bg-[#f7f7f8] mx-auto max-w-3xl md:max-w-2xl px-3">
      <div className="flex-1 overflow-hidden py-3">
        <div className="flex flex-col space-y-5 max-w-2xl mx-auto">
          {/* Welcome card shows when there are no messages */}
          {messages.length === 0 && <WelcomeCard />}

          {messages.map((message, index) => (
            <MessageDisplay
              key={index}
              message={message}
              className={cn("message-appear")}
              onTextSelect={handleTextSelect}
            />
          ))}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Increased spacing before input bar */}
      <div className="h-16"></div>

      {/* Floating input bar */}
      <div className="sticky bottom-6 px-3 mb-4 z-20">
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden transition-all duration-200">
          {/* Custom resize handle at top */}
          <div
            ref={resizeHandleRef}
            className="w-full h-6 flex items-center justify-center cursor-ns-resize hover:bg-gray-100 transition-colors"
          >
            <GripHorizontal className="h-3 w-3 text-gray-400" />
          </div>

          <form onSubmit={handleSubmit} className="relative flex flex-col">
            {/* Show quoted text indicator if text is selected */}
            {selectedText && (
              <div className="px-4 pt-3">
                <QuotedTextIndicator
                  text={selectedText}
                  onClear={clearSelectedText}
                />
              </div>
            )}

            {/* Textarea field with custom height */}
            <div className="flex-1 relative">
              <Textarea
                ref={(el) => {
                  textareaRef.current = el;
                  if (inputRef) {
                    // @ts-ignore - this is a valid assignment
                    inputRef.current = el;
                  }
                }}
                value={input}
                onChange={(e) => {
                  setInput(e.target.value);

                  // Auto-resize based on content
                  if (textareaRef.current) {
                    // Reset height to get the correct scrollHeight
                    textareaRef.current.style.height = 'auto';
                    // Set new height based on scrollHeight, with min and max constraints
                    const newHeight = Math.min(
                      Math.max(44, textareaRef.current.scrollHeight),
                      200
                    );
                    setTextareaHeight(newHeight);
                    textareaRef.current.style.height = `${newHeight}px`;
                  }
                }}
                placeholder={selectedImage ? "Describe what you'd like to know about this image..." : "Ask a university-related question (Shift+Enter for new line)"}
                disabled={isProcessing}
                className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 py-3 px-4 text-base bg-transparent min-h-[44px] max-h-[200px] overflow-y-auto whitespace-pre-wrap"
                onKeyDown={handleKeyDown}
                rows={1}
                style={{
                  height: `${textareaHeight}px`,
                  resize: 'none' // Disable default resize
                }}
              />
            </div>


            {/* Bottom bar with attach and send buttons */}
            <div className="flex items-center justify-between px-4 py-2 border-t border-gray-100">
              {/* Paperclip icon for image upload */}
              <div className="flex items-center gap-2">
                <ImageUploadButton
                  onImageSelect={handleImageSelect}
                  disabled={isProcessing}
                  className={cn(
                    "text-gray-400 hover:text-gray-700",
                    selectedImage && "text-blue-500"
                  )}
                >
                  <Paperclip className="h-5 w-5" />
                </ImageUploadButton>
                {selectedImage && (
                  <span className="text-xs text-blue-500 font-medium">
                    Image attached
                  </span>
                )}
              </div>

              {/* Send button */}
              <div>
                {isProcessing ? (
                  <div className="h-8 w-8 flex items-center justify-center">
                    <div className="animate-spin h-4 w-4 border-2 border-gray-500 rounded-full border-t-transparent"></div>
                  </div>
                ) : (
                  <Button
                    type="submit"
                    disabled={!input.trim() && !selectedImage}
                    size="icon"
                    variant="ghost"
                    className="h-8 w-8 text-black bg-black/5 hover:bg-black/10 rounded-full"
                  >
                    <Send className="h-4 w-4" />
                    <span className="sr-only">Send</span>
                  </Button>
                )}
              </div>
            </div>
          </form>
        </div>

        {/* Preview area for image */}
        {previewImage && (
          <div className="mt-2 bg-white p-2 rounded-lg shadow-sm inline-block">
            <div className="relative inline-block">
              <img
                src={previewImage}
                alt="Preview"
                className="h-20 rounded-md object-cover border border-gray-200"
              />
              <button
                type="button"
                onClick={clearSelectedImage}
                className="absolute -top-2 -right-2 rounded-full bg-gray-800 text-white h-5 w-5 flex items-center justify-center text-xs"
              >
                <span>×</span>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Settings and new chat buttons (fixed to the viewport) */}
      <div className="fixed top-4 right-4 flex gap-2 z-30">
        {/* New Chat Button */}
        <Button
          onClick={clearAllMessages}
          className="h-8 w-8 p-0 rounded-full"
          variant="outline"
          title="New Chat"
        >
          <Plus className="h-4 w-4" />
          <span className="sr-only">New Chat</span>
        </Button>

        {/* Remove API Key Settings Modal */}
      </div>
    </div>
  );
};

export default ChatInterface;
