
import React, { useEffect, useState, useCallback, useRef } from "react";
import { cn } from "@/lib/utils";
import { marked } from "marked";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import TextSelectionPopup from "./TextSelectionPopup";

interface Message {
  role: "user" | "assistant";
  content: string;
  image?: string;
}

interface MessageDisplayProps {
  message: Message;
  className?: string;
  onTextSelect?: (selectedText: string) => void;
}

// Component for code blocks with copy functionality
const CodeBlock = ({ language, code }: { language: string; code: string }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = useCallback(() => {
    navigator.clipboard.writeText(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  }, [code]);

  return (
    <div className="code-block-wrapper">
      <div className="code-block-header">
        <span className="code-language">{language || 'text'}</span>
        <button
          className={cn("copy-button", copied && "copied")}
          onClick={handleCopy}
        >
          {copied ? 'Copied!' : 'Copy'}
        </button>
      </div>
      <SyntaxHighlighter
        language={language || 'text'}
        style={vscDarkPlus}
        customStyle={{
          margin: 0,
          borderRadius: '0 0 0.375rem 0.375rem',
          background: '#1e1e1e'
        }}
        codeTagProps={{
          style: {
            color: '#d4d4d4',
            background: 'transparent'
          }
        }}
        wrapLines={true}
        wrapLongLines={true}
      >
        {code}
      </SyntaxHighlighter>
    </div>
  );
};

// Custom renderer for marked
const createCustomRenderer = () => {
  const renderer = new marked.Renderer();

  // Enhanced blockquote rendering with support for callouts
  renderer.blockquote = (quote) => {
    // Check for callout markers
    const noteMatch = quote.match(/<p>📝 NOTE: (.*?)<\/p>/);
    const warningMatch = quote.match(/<p>⚠️ WARNING: (.*?)<\/p>/);
    const tipMatch = quote.match(/<p>💡 TIP: (.*?)<\/p>/);
    const importantMatch = quote.match(/<p>🔑 IMPORTANT: (.*?)<\/p>/);

    if (noteMatch) {
      return `<div class="callout callout-note">
        <div class="callout-header">
          <span class="callout-icon">📝</span>
          <span class="callout-title">NOTE</span>
        </div>
        <div class="callout-content">${noteMatch[1]}</div>
      </div>`;
    } else if (warningMatch) {
      return `<div class="callout callout-warning">
        <div class="callout-header">
          <span class="callout-icon">⚠️</span>
          <span class="callout-title">WARNING</span>
        </div>
        <div class="callout-content">${warningMatch[1]}</div>
      </div>`;
    } else if (tipMatch) {
      return `<div class="callout callout-tip">
        <div class="callout-header">
          <span class="callout-icon">💡</span>
          <span class="callout-title">TIP</span>
        </div>
        <div class="callout-content">${tipMatch[1]}</div>
      </div>`;
    } else if (importantMatch) {
      return `<div class="callout callout-important">
        <div class="callout-header">
          <span class="callout-icon">🔑</span>
          <span class="callout-title">IMPORTANT</span>
        </div>
        <div class="callout-content">${importantMatch[1]}</div>
      </div>`;
    } else {
      return `<blockquote>${quote}</blockquote>`;
    }
  };

  // Enhanced table rendering
  renderer.table = (header, body) => {
    return `<div class="table-container">
      <table>
        <thead>${header}</thead>
        <tbody>${body}</tbody>
      </table>
    </div>`;
  };

  return renderer;
};

const MessageDisplay = ({ message, className, onTextSelect }: MessageDisplayProps) => {
  const isUser = message.role === "user";
  const [htmlContent, setHtmlContent] = useState("");
  const [codeBlocks, setCodeBlocks] = useState<Array<{language: string; code: string}>>([]);
  const [selectionPosition, setSelectionPosition] = useState<{ x: number; y: number } | null>(null);
  const [showSelectionPopup, setShowSelectionPopup] = useState(false);
  const messageRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isUser) {
      // Extract code blocks before rendering markdown
      const codeBlockRegex = /```([a-z]*)\n([\s\S]*?)```/g;
      const extractedCodeBlocks: Array<{language: string; code: string}> = [];
      let codeBlockCounter = 0;

      // Replace code blocks with placeholders
      const contentWithPlaceholders = message.content.replace(codeBlockRegex, (match, language, code) => {
        extractedCodeBlocks.push({
          language: language.trim(),
          code: code.trim()
        });
        return `<div id="code-block-${codeBlockCounter++}"></div>`;
      });

      setCodeBlocks(extractedCodeBlocks);

      // Configure marked options with custom renderer
      const renderer = createCustomRenderer();
      marked.setOptions({
        breaks: true,
        gfm: true,
        renderer: renderer,
      });

      // Parse markdown to HTML - handle potential Promise
      const parsedContent = marked.parse(contentWithPlaceholders);
      if (parsedContent instanceof Promise) {
        parsedContent.then(content => setHtmlContent(content));
      } else {
        setHtmlContent(parsedContent);
      }
    }
  }, [message.content, isUser]);

  // Handle text selection
  useEffect(() => {
    const handleSelectionChange = () => {
      const selection = window.getSelection();

      if (selection && selection.toString().trim().length > 0 && messageRef.current) {
        // Check if the selection is within this message component
        let node = selection.anchorNode;
        let isWithinMessage = false;

        while (node) {
          if (node === messageRef.current) {
            isWithinMessage = true;
            break;
          }
          node = node.parentNode;
        }

        if (isWithinMessage) {
          // Position the popup above the mouse cursor using the stored position
          const mouseX = window.lastMouseX || window.innerWidth / 2;
          const mouseY = window.lastMouseY || window.innerHeight / 2;

          setSelectionPosition({
            x: mouseX,
            y: mouseY - 40 // Position 40px above the cursor
          });
          setShowSelectionPopup(true);
        } else {
          setShowSelectionPopup(false);
        }
      } else {
        setShowSelectionPopup(false);
      }
    };

    // Add event listener for selection changes
    document.addEventListener('selectionchange', handleSelectionChange);

    // Also handle mouseup event to catch selections that might be missed by selectionchange
    const handleMouseUp = (e: MouseEvent) => {
      // Store the mouse position in a variable that can be accessed by handleSelectionChange
      window.lastMouseX = e.clientX;
      window.lastMouseY = e.clientY;
      setTimeout(handleSelectionChange, 10); // Small delay to ensure selection is complete
    };

    if (messageRef.current) {
      messageRef.current.addEventListener('mouseup', handleMouseUp);
    }

    // Cleanup
    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
      if (messageRef.current) {
        messageRef.current.removeEventListener('mouseup', handleMouseUp);
      }
    };
  }, []);

  // Handle clicking the reply button
  const handleReply = () => {
    const selection = window.getSelection();
    if (selection && onTextSelect) {
      const selectedText = selection.toString().trim();
      if (selectedText.length > 0) {
        onTextSelect(selectedText);
        setShowSelectionPopup(false);
      }
    }
  };

  // Render the message with code blocks
  const renderMessageContent = () => {
    if (isUser) {
      return (
        <>
          {message.image && (
            <div className="mb-2">
              <img
                src={message.image}
                alt="User uploaded"
                className="max-w-full max-h-80 rounded-md object-contain"
              />
            </div>
          )}
          <p className="whitespace-pre-wrap break-words">{message.content}</p>
        </>
      );
    }

    // If there are no code blocks, just render the HTML content
    if (codeBlocks.length === 0) {
      return (
        <div
          className="markdown-content"
          dangerouslySetInnerHTML={{ __html: htmlContent }}
        />
      );
    }

    // If there are code blocks, we need to render them as React components
    const contentParts = htmlContent.split(/<div id="code-block-(\d+)"><\/div>/);

    return (
      <div className="markdown-content">
        {contentParts.map((part, index) => {
          // Even indices are HTML content
          if (index % 2 === 0) {
            return <div key={`html-${index}`} dangerouslySetInnerHTML={{ __html: part }} />;
          }

          // Odd indices are code block placeholders
          const codeBlockIndex = parseInt(part, 10);
          const codeBlock = codeBlocks[codeBlockIndex];

          return (
            <CodeBlock
              key={`code-${codeBlockIndex}`}
              language={codeBlock.language}
              code={codeBlock.code}
            />
          );
        })}
      </div>
    );
  };

  return (
    <div className={cn("flex", className)}>
      <div
        ref={messageRef}
        className={cn(
          "w-full px-4 py-2 rounded-lg text-gray-800 text-base",
          isUser ? "bg-white" : "bg-gray-100"
        )}
      >
        {renderMessageContent()}

        {/* Text selection popup */}
        <TextSelectionPopup
          position={selectionPosition}
          onReply={handleReply}
          visible={showSelectionPopup}
        />
      </div>
    </div>
  );
};

export default MessageDisplay;
